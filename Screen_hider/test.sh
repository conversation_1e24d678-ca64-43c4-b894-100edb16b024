#!/bin/bash

# ScreenHider Test Script
# This script runs various tests to validate the application functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

echo "🧪 ScreenHider Test Suite"
echo "========================"

# Test 1: Check if app bundle exists
log_info "Test 1: Checking app bundle..."
if [ -d "ScreenHider.app" ]; then
    log_success "App bundle exists"
else
    log_error "App bundle not found. Run ./build.sh first."
    exit 1
fi

# Test 2: Check executable permissions
log_info "Test 2: Checking executable permissions..."
if [ -x "ScreenHider.app/Contents/MacOS/ScreenHider" ]; then
    log_success "Executable has correct permissions"
else
    log_error "Executable is not executable"
    exit 1
fi

# Test 3: Check Info.plist validity
log_info "Test 3: Validating Info.plist..."
if plutil -lint "ScreenHider.app/Contents/Info.plist" > /dev/null 2>&1; then
    log_success "Info.plist is valid"
else
    log_error "Info.plist is invalid"
    exit 1
fi

# Test 4: Check bundle identifier
log_info "Test 4: Checking bundle identifier..."
BUNDLE_ID=$(plutil -extract CFBundleIdentifier raw "ScreenHider.app/Contents/Info.plist")
if [ "$BUNDLE_ID" = "com.screenhider.app" ]; then
    log_success "Bundle identifier is correct: $BUNDLE_ID"
else
    log_warning "Bundle identifier mismatch: $BUNDLE_ID"
fi

# Test 5: Check minimum macOS version
log_info "Test 5: Checking minimum macOS version..."
MIN_VERSION=$(plutil -extract LSMinimumSystemVersion raw "ScreenHider.app/Contents/Info.plist")
if [ "$MIN_VERSION" = "11.0" ]; then
    log_success "Minimum macOS version is correct: $MIN_VERSION"
else
    log_warning "Minimum macOS version: $MIN_VERSION"
fi

# Test 6: Check if app can launch (quick test)
log_info "Test 6: Testing app launch (5 second test)..."
timeout 5s ./ScreenHider.app/Contents/MacOS/ScreenHider &
APP_PID=$!
sleep 2

if kill -0 $APP_PID 2>/dev/null; then
    log_success "App launched successfully"
    kill $APP_PID 2>/dev/null || true
    wait $APP_PID 2>/dev/null || true
else
    log_warning "App may have crashed during launch"
fi

# Test 7: Check for required frameworks
log_info "Test 7: Checking framework dependencies..."
if otool -L "ScreenHider.app/Contents/MacOS/ScreenHider" | grep -q "Cocoa"; then
    log_success "Cocoa framework linked"
else
    log_error "Cocoa framework not found"
fi

# Test 8: Check architecture
log_info "Test 8: Checking binary architecture..."
if command -v lipo &> /dev/null; then
    ARCHS=$(lipo -archs "ScreenHider.app/Contents/MacOS/ScreenHider")
    log_success "Architectures: $ARCHS"
else
    log_warning "lipo command not available, skipping architecture check"
fi

# Test 9: Check code signature (if signed)
log_info "Test 9: Checking code signature..."
if codesign -dv "ScreenHider.app" 2>/dev/null; then
    log_success "App is code signed"
else
    log_info "App is not code signed (this is okay for development)"
fi

# Test 10: Check app size
log_info "Test 10: Checking app size..."
APP_SIZE=$(du -sh "ScreenHider.app" | cut -f1)
log_info "App size: $APP_SIZE"

echo ""
log_success "All tests completed!"
echo ""
echo "🚀 Manual testing steps:"
echo "1. Run: open ScreenHider.app"
echo "2. Look for the rectangle icon in your menu bar"
echo "3. Click the icon to toggle overlay"
echo "4. Test with screen sharing application"
echo "5. Verify overlay is hidden from screen capture"
echo ""
echo "🔍 To test screen capture exclusion:"
echo "1. Enable overlay"
echo "2. Take a screenshot (Cmd+Shift+3)"
echo "3. Check if overlay appears in screenshot (it shouldn't)"
