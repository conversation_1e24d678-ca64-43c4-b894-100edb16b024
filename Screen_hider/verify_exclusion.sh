#!/bin/bash

# Quick verification script for ScreenHider screen capture exclusion
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

echo "🔍 ScreenHider v1.1.1 Screen Capture Exclusion Verification"
echo "==========================================================="

# Check if ScreenHider is running
if ! pgrep -f "ScreenHider" > /dev/null; then
    log_error "ScreenHider is not running. Please start it first."
    exit 1
fi

log_success "ScreenHider v1.1.1 is running"

# Check version
VERSION=$(plutil -extract CFBundleShortVersionString raw /Applications/ScreenHider.app/Contents/Info.plist 2>/dev/null || echo "unknown")
log_info "Version: $VERSION"

echo ""
echo "🧪 TESTING INSTRUCTIONS:"
echo "========================"

echo ""
echo "1️⃣  ENABLE THE OVERLAY:"
echo "   • Right-click the ScreenHider menu bar icon"
echo "   • Click 'Show Overlay'"
echo "   • Verify you can see the overlay on your screen"

echo ""
echo "2️⃣  TEST SCREENSHOT EXCLUSION:"
echo "   • Press Cmd+Shift+3 to take a screenshot"
echo "   • Open the screenshot from your desktop"
echo "   • The overlay should NOT appear in the screenshot"

echo ""
echo "3️⃣  TEST ZOOM/TEAMS EXCLUSION:"
echo "   • Open Zoom, Teams, Google Meet, or any screen sharing app"
echo "   • Start screen sharing"
echo "   • Ask participants if they can see the overlay"
echo "   • The overlay should be INVISIBLE to participants"

echo ""
echo "4️⃣  VERIFY ALWAYS ON TOP:"
echo "   • Open other applications (browser, text editor, etc.)"
echo "   • The overlay should stay above all other windows"

echo ""
echo "🎯 EXPECTED RESULTS:"
echo "==================="
echo "✅ Overlay visible on YOUR screen"
echo "❌ Overlay NOT visible in screenshots"
echo "❌ Overlay NOT visible to screen sharing participants"
echo "✅ Overlay always stays on top of other windows"

echo ""
echo "🔧 NEW FEATURES IN v1.1.1:"
echo "=========================="
echo "• Enhanced screen capture exclusion using .screenSaver window level"
echo "• Multiple exclusion methods for maximum compatibility"
echo "• Periodic verification every 5 seconds"
echo "• Comprehensive window properties for capture prevention"
echo "• Extended opacity range (0% to 100%)"

echo ""
echo "🚨 IF OVERLAY IS STILL VISIBLE IN SCREEN SHARING:"
echo "================================================"
echo "1. Restart ScreenHider completely"
echo "2. Make sure you're using the overlay in normal mode (not resize/workspace)"
echo "3. Check Console.app for any ScreenHider error messages"
echo "4. Try different opacity levels (avoid 0% for testing)"

echo ""
log_info "Test completed. The overlay should now be completely hidden from all screen capture methods!"
