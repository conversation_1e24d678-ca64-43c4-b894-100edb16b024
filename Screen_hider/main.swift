import Cocoa
import os.log

// MARK: - Application Entry Point

/// Custom logger for the application
let logger = Logger(subsystem: "com.screenhider.app", category: "main")

/// Entry point for the ScreenHider application
func main() {
    logger.info("🚀 ScreenHider application starting...")

    // Verify macOS version compatibility
    guard #available(macOS 11.0, *) else {
        logger.error("❌ ScreenHider requires macOS 11.0 or later")
        NSAlert.showCriticalAlert(
            title: "Incompatible macOS Version",
            message: "ScreenHider requires macOS Big Sur (11.0) or later to function properly.",
            informativeText: "The screen capture exclusion feature requires APIs only available in macOS 11.0+."
        )
        exit(1)
    }

    // Initialize the application
    let app = NSApplication.shared
    let delegate = AppDelegate()
    app.delegate = delegate

    // Configure app to run without dock icon (menu bar app)
    app.setActivationPolicy(.accessory)

    logger.info("✅ Application configured successfully")

    // Run the application
    app.run()
}

// MARK: - Extensions

extension NSAlert {
    /// Shows a critical alert dialog
    static func showCriticalAlert(title: String, message: String, informativeText: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = message + "\n\n" + informativeText
        alert.alertStyle = .critical
        alert.addButton(withTitle: "Quit")
        alert.runModal()
    }
}

// Start the application
main()
