#!/bin/bash

# ScreenHider Installation Script
# This script installs ScreenHider to the Applications folder and sets up launch options

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

APP_NAME="ScreenHider"
INSTALL_PATH="/Applications/${APP_NAME}.app"
LAUNCH_AGENT_PATH="$HOME/Library/LaunchAgents/com.screenhider.app.plist"

echo "📦 ScreenHider Installation"
echo "==========================="

# Check if app bundle exists
if [ ! -d "${APP_NAME}.app" ]; then
    log_error "App bundle not found. Please run ./build.sh first."
    exit 1
fi

# Check if already installed
if [ -d "$INSTALL_PATH" ]; then
    log_warning "ScreenHider is already installed at $INSTALL_PATH"
    echo "Do you want to replace it? (y/N): "
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        log_info "Installation cancelled"
        exit 0
    fi
    
    log_info "Removing existing installation..."
    rm -rf "$INSTALL_PATH"
fi

# Copy app to Applications
log_info "Installing ScreenHider to Applications folder..."
cp -R "${APP_NAME}.app" "$INSTALL_PATH"

# Verify installation
if [ -d "$INSTALL_PATH" ]; then
    log_success "ScreenHider installed successfully to $INSTALL_PATH"
else
    log_error "Installation failed"
    exit 1
fi

# Set proper permissions
log_info "Setting permissions..."
chmod -R 755 "$INSTALL_PATH"
chmod +x "$INSTALL_PATH/Contents/MacOS/$APP_NAME"

# Ask about launch at login
echo ""
echo "Do you want ScreenHider to start automatically at login? (y/N): "
read -r launch_response

if [[ "$launch_response" =~ ^[Yy]$ ]]; then
    log_info "Creating launch agent..."
    
    # Create launch agent plist
    cat > "$LAUNCH_AGENT_PATH" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.screenhider.app</string>
    <key>ProgramArguments</key>
    <array>
        <string>$INSTALL_PATH/Contents/MacOS/$APP_NAME</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <false/>
    <key>LaunchOnlyOnce</key>
    <true/>
</dict>
</plist>
EOF
    
    # Load the launch agent
    launchctl load "$LAUNCH_AGENT_PATH" 2>/dev/null || log_warning "Could not load launch agent automatically"
    
    log_success "Launch agent created. ScreenHider will start at next login."
fi

# Create uninstaller
log_info "Creating uninstaller..."
cat > "/tmp/uninstall_screenhider.sh" << 'EOF'
#!/bin/bash

echo "🗑️  Uninstalling ScreenHider..."

# Kill running instance
pkill -f "ScreenHider" 2>/dev/null || true

# Remove app
rm -rf "/Applications/ScreenHider.app"

# Remove launch agent
launchctl unload "$HOME/Library/LaunchAgents/com.screenhider.app.plist" 2>/dev/null || true
rm -f "$HOME/Library/LaunchAgents/com.screenhider.app.plist"

# Remove preferences
defaults delete com.screenhider.app 2>/dev/null || true

echo "✅ ScreenHider uninstalled successfully"
echo "This uninstaller will self-destruct in 5 seconds..."
sleep 5
rm "$0"
EOF

chmod +x "/tmp/uninstall_screenhider.sh"

# Installation complete
echo ""
log_success "Installation completed successfully!"
echo ""
echo "🚀 Next steps:"
echo "   • Launch ScreenHider from Applications folder"
echo "   • Or run: open '$INSTALL_PATH'"
echo "   • Look for the rectangle icon in your menu bar"
echo ""
echo "📋 Usage:"
echo "   • Left-click menu bar icon to toggle overlay"
echo "   • Right-click for full menu options"
echo "   • Use Cmd+Shift+H keyboard shortcut"
echo ""
echo "🗑️  To uninstall later:"
echo "   • Run: /tmp/uninstall_screenhider.sh"
echo ""

# Ask to launch now
echo "Do you want to launch ScreenHider now? (Y/n): "
read -r launch_now
if [[ ! "$launch_now" =~ ^[Nn]$ ]]; then
    log_info "Launching ScreenHider..."
    open "$INSTALL_PATH"
    sleep 2
    
    if pgrep -f "ScreenHider" > /dev/null; then
        log_success "ScreenHider is now running! Check your menu bar."
    else
        log_warning "ScreenHider may not have started. Try launching manually."
    fi
fi

echo ""
log_success "Installation process complete!"
