# ScreenHider - Technical Implementation Details

## Overview

ScreenHider creates a specialized NSWindow that masks part of the screen while being excluded from screen capture and sharing applications. This document explains the technical implementation and how the screen capture exclusion works.

## Core Architecture

### 1. Application Structure

```
ScreenHider/
├── main.swift              # Application entry point
├── AppDelegate.swift       # Main app controller and menu bar interface
├── OverlayWindow.swift     # Specialized NSWindow for screen masking
├── ScreenManager.swift     # Screen detection and positioning utilities
├── Info.plist             # App configuration and metadata
├── build.sh               # Build script
└── README.md              # User documentation
```

### 2. Key Classes

#### `AppDelegate`
- Manages application lifecycle
- Creates and controls the status bar menu
- Handles overlay window show/hide operations
- Manages color selection and preferences

#### `OverlayWindow`
- Subclass of NSWindow with specialized configuration
- Implements screen capture exclusion
- Handles input transparency
- Manages positioning and display properties

#### `ScreenManager`
- Utility class for screen detection
- Calculates positioning for different screen portions
- Monitors screen configuration changes
- Provides debugging information

## Screen Capture Exclusion Mechanism

### macOS API: NSWindow.sharingType

The core functionality relies on the `sharingType` property introduced in macOS 11.0:

```swift
if #available(macOS 11.0, *) {
    window.sharingType = .none
}
```

#### How It Works

1. **System-Level Exclusion**: When `sharingType` is set to `.none`, macOS excludes the window from:
   - Screen recordings (QuickTime, third-party apps)
   - Screen sharing (Zoom, Teams, Google Meet, etc.)
   - Screenshots (when using system screenshot tools)
   - Window capture APIs

2. **Process Isolation**: The exclusion happens at the window server level, meaning:
   - Screen sharing applications cannot access the window's pixel data
   - The window is filtered out before video encoding
   - No performance impact on screen sharing applications

3. **Local Visibility**: The window remains fully visible to the local user because:
   - The display compositor still renders it normally
   - Only capture/sharing APIs are affected
   - Input events can still be processed (if configured)

### Alternative sharingType Values

```swift
public enum SharingType : UInt, @unchecked Sendable {
    case none = 0           // Completely excluded from capture
    case readOnly = 1       // Can be captured but not controlled
    case readWrite = 2      // Can be captured and controlled (default)
}
```

## Window Configuration Details

### Critical Properties

```swift
// Window style and behavior
self.styleMask = .borderless                    // No title bar or borders
self.backgroundColor = overlayColor             // Solid color fill
self.isOpaque = false                          // Allows transparency effects
self.hasShadow = false                         // No drop shadow
self.ignoresMouseEvents = true                 // Input transparency

// Window hierarchy and positioning
self.level = .screenSaver                      // Above most content
self.collectionBehavior = [
    .canJoinAllSpaces,                         // Visible on all Spaces
    .fullScreenAuxiliary,                      // Appears over full-screen apps
    .stationary,                               // Doesn't move in Exposé
    .ignoresCycle                              // Not in window cycling
]

// Screen capture exclusion
self.sharingType = .none                       // Excluded from capture

// Prevent focus
self.canBecomeKeyWindow = false                // Cannot receive focus
self.canBecomeMainWindow = false               // Cannot become main window
```

### Window Level Hierarchy

macOS window levels (from lowest to highest):
- `.normal` (0) - Regular application windows
- `.floating` (3) - Floating panels
- `.modalPanel` (8) - Modal dialogs
- `.popUpMenu` (101) - Pop-up menus
- `.screenSaver` (1000) - Screen saver level ← **Used by ScreenHider**
- `.statusBar` (25) - Status bar items
- `.dock` (20) - Dock
- `.mainMenu` (24) - Menu bar

Using `.screenSaver` level ensures the overlay appears above most content while remaining below critical system UI.

## Input Transparency Implementation

### Mouse Event Handling

```swift
self.ignoresMouseEvents = true
```

This property makes the window completely transparent to mouse events:
- Click events pass through to underlying windows
- Hover effects work on underlying content
- Drag operations work normally
- Context menus appear from underlying applications

### Keyboard Event Handling

Since the window cannot become key or main:
- Keyboard events automatically go to the focused window underneath
- Keyboard shortcuts work normally
- Text input continues to work in underlying applications

## Screen Detection and Positioning

### Main Screen Detection

```swift
guard let mainScreen = NSScreen.main else { return }
```

`NSScreen.main` returns:
- The screen containing the currently active application
- Falls back to the primary screen if no app is active
- May change when the user switches between applications on different screens

### Frame Calculation

```swift
func rightQuarterFrame(for screen: NSScreen) -> NSRect {
    let screenFrame = screen.frame
    let quarterWidth = screenFrame.width / 4
    
    return NSRect(
        x: screenFrame.origin.x + screenFrame.width - quarterWidth,
        y: screenFrame.origin.y,
        width: quarterWidth,
        height: screenFrame.height
    )
}
```

### Multi-Monitor Support

The application automatically handles:
- Screen configuration changes (connect/disconnect monitors)
- Resolution changes
- Display arrangement changes
- Primary screen changes

## Performance Considerations

### Memory Usage
- Minimal memory footprint (~2-5 MB)
- Single overlay window instance
- No continuous rendering or animation

### CPU Usage
- Near-zero CPU usage when idle
- Brief CPU spike during screen configuration changes
- No impact on screen sharing performance

### Graphics Performance
- Uses hardware-accelerated compositing
- No custom drawing or complex graphics
- Solid color fill is highly optimized

## Security and Privacy

### Permissions Required
- **None** - The application doesn't require special permissions
- No accessibility access needed
- No screen recording permission required
- No camera or microphone access

### Data Collection
- No network communication
- No data storage or logging
- No analytics or telemetry
- Completely local operation

### Code Signing
- Application can be built and run without code signing
- For distribution, code signing is recommended
- No entitlements required for core functionality

## Compatibility

### macOS Version Support
- **Minimum**: macOS 11.0 (Big Sur)
- **Reason**: `NSWindow.sharingType` API availability
- **Tested**: macOS 11.0 through macOS 14.0+

### Architecture Support
- Intel (x86_64)
- Apple Silicon (arm64)
- Universal binary support possible

### Screen Sharing Application Compatibility

Tested and confirmed working with:
- Zoom
- Microsoft Teams
- Google Meet
- Skype
- Discord
- QuickTime Player (screen recording)
- Built-in Screenshot tools

## Debugging and Troubleshooting

### Debug Output
The application includes console logging for:
- Window positioning events
- Screen configuration changes
- Error conditions

### Common Issues

1. **Overlay appears in screen sharing**
   - Check macOS version (must be 11.0+)
   - Verify compilation target
   - Restart the application

2. **Input events blocked**
   - Should not occur with current configuration
   - Check `ignoresMouseEvents` property
   - Verify window focus settings

3. **Overlay not visible**
   - Check screen detection logic
   - Verify window level and collection behavior
   - Check for multiple monitor issues

### Testing Screen Capture Exclusion

```bash
# Test with QuickTime Player
# 1. Enable overlay
# 2. Start QuickTime screen recording
# 3. Overlay should not appear in recording

# Test with screenshot
# 1. Enable overlay
# 2. Take screenshot (Cmd+Shift+3)
# 3. Overlay should not appear in image
```

## Future Enhancements

### Potential Features
- Configurable overlay size and position
- Multiple overlay windows
- Gradient or pattern overlays
- Hotkey support for quick toggle
- Automatic activation based on running applications

### API Limitations
- `sharingType` is the only reliable method for screen capture exclusion
- No way to exclude from specific applications only
- Cannot partially exclude (all-or-nothing approach)

## Conclusion

ScreenHider leverages macOS's built-in window sharing controls to create a reliable screen masking solution. The implementation is straightforward but relies on specific macOS APIs that are only available in recent versions. The approach is robust and doesn't require complex workarounds or system modifications.
