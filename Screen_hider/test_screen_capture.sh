#!/bin/bash

# Quick Screen Capture Test for ScreenHider
# This script tests if the overlay is properly excluded from screen capture

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

echo "🧪 ScreenHider Screen Capture Exclusion Test"
echo "============================================"

# Check if ScreenHider is running
if ! pgrep -f "ScreenHider" > /dev/null; then
    log_error "ScreenHider is not running. Please start it first."
    exit 1
fi

log_success "ScreenHider is running"

# Create test directory
TEST_DIR="/tmp/screenhider_test"
mkdir -p "$TEST_DIR"

log_info "Test 1: Taking screenshot with overlay enabled"
echo "Please:"
echo "1. Enable the ScreenHider overlay (right-click menu bar icon → Show Overlay)"
echo "2. Make sure the overlay is visible on your screen"
echo "3. Press Enter when ready..."
read -r

# Take screenshot
screencapture -x "$TEST_DIR/screenshot_with_overlay.png"
log_success "Screenshot saved to $TEST_DIR/screenshot_with_overlay.png"

log_info "Test 2: Analyzing screenshot"
# Check if the screenshot was created
if [ -f "$TEST_DIR/screenshot_with_overlay.png" ]; then
    # Get image dimensions and file size
    if command -v sips &> /dev/null; then
        IMAGE_INFO=$(sips -g pixelWidth -g pixelHeight "$TEST_DIR/screenshot_with_overlay.png" 2>/dev/null)
        log_info "Screenshot info: $IMAGE_INFO"
    fi
    
    # Open the screenshot for manual inspection
    log_info "Opening screenshot for manual inspection..."
    open "$TEST_DIR/screenshot_with_overlay.png"
    
    echo ""
    echo "🔍 MANUAL VERIFICATION REQUIRED:"
    echo "Look at the screenshot that just opened."
    echo "The ScreenHider overlay should NOT be visible in the screenshot."
    echo ""
    echo "Is the overlay MISSING from the screenshot? (y/n)"
    read -r RESPONSE
    
    if [[ "$RESPONSE" =~ ^[Yy]$ ]]; then
        log_success "✅ SCREEN CAPTURE EXCLUSION IS WORKING!"
        log_success "The overlay is properly excluded from screenshots."
    else
        log_error "❌ SCREEN CAPTURE EXCLUSION IS NOT WORKING!"
        log_error "The overlay appears in the screenshot - this needs to be fixed."
        
        echo ""
        log_info "Troubleshooting steps:"
        echo "1. Make sure you're running macOS 11.0 or later"
        echo "2. Try restarting ScreenHider"
        echo "3. Check Console.app for ScreenHider error messages"
        echo "4. Verify the overlay is in normal mode (not resize or workspace mode)"
    fi
else
    log_error "Failed to create screenshot"
fi

log_info "Test 3: Zoom/Teams compatibility test"
echo ""
echo "🎥 ZOOM/TEAMS TEST (Manual):"
echo "1. Open Zoom, Teams, or Google Meet"
echo "2. Start screen sharing"
echo "3. Ensure the ScreenHider overlay is visible on YOUR screen"
echo "4. Ask other participants if they can see the overlay"
echo "5. The overlay should be INVISIBLE to other participants"
echo ""
echo "If participants can see the overlay, the screen capture exclusion is not working properly."

echo ""
log_info "Test files saved in: $TEST_DIR"
log_info "You can delete this directory when done: rm -rf $TEST_DIR"

echo ""
echo "🎯 Expected Results:"
echo "✅ Overlay visible on your local screen"
echo "❌ Overlay NOT visible in screenshots"
echo "❌ Overlay NOT visible to screen sharing participants"
