# ScreenHider

<div align="center">

![ScreenHider Logo](https://img.shields.io/badge/ScreenHider-v1.0.0-blue?style=for-the-badge)
![macOS](https://img.shields.io/badge/macOS-11.0+-green?style=for-the-badge&logo=apple)
![Swift](https://img.shields.io/badge/Swift-5.0+-orange?style=for-the-badge&logo=swift)
![License](https://img.shields.io/badge/License-MIT-red?style=for-the-badge)

**A powerful macOS application that creates an intelligent screen overlay to hide portions of your screen from screen sharing applications while maintaining full local interaction.**

[Features](#features) • [Installation](#installation) • [Usage](#usage) • [Building](#building) • [Troubleshooting](#troubleshooting)

</div>

---

## 🌟 Features

### Core Functionality
- **🔒 Screen Capture Exclusion**: Overlay is completely invisible to screen sharing and recording applications
- **🖱️ Input Transparency**: Mouse clicks and keyboard input pass through the overlay to underlying applications
- **🖥️ Multi-Space Support**: Works seamlessly across all macOS Spaces and full-screen applications
- **📱 Menu Bar Interface**: Clean, intuitive menu bar app with comprehensive controls
- **🎨 Customizable Appearance**: Multiple overlay colors and opacity levels
- **📍 Dynamic Positioning**: Automatically adjusts when screen configuration changes
- **👻 Silent Operation**: Runs invisibly in the background as a menu bar application

### Advanced Features
- **⌨️ Global Hotkeys**: Quick toggle with Cmd+Shift+H keyboard shortcut
- **🔄 Auto-Launch**: Optional startup at login
- **💾 Persistent Settings**: Remembers your preferences between sessions
- **🖼️ Multiple Screen Support**: Intelligent handling of multi-monitor setups
- **🎯 Precision Positioning**: Covers exactly the right quarter of your main screen
- **⚡ High Performance**: Minimal CPU and memory usage

## 📋 System Requirements

- **Operating System**: macOS Big Sur (11.0) or later
- **Architecture**: Intel (x86_64) or Apple Silicon (arm64)
- **Development**: Xcode 12.0+ or Swift 5.0+ (for building from source)
- **Memory**: Minimal (< 10MB RAM usage)
- **Permissions**: No special permissions required

## 🔧 How It Works

### Screen Capture Exclusion Technology

ScreenHider leverages macOS's advanced window management APIs to create a sophisticated overlay system:

#### Core Technology
- **`NSWindow.sharingType = .none`**: Utilizes macOS 11.0+ API for complete screen capture exclusion
- **Window Server Integration**: Works at the system level for reliable exclusion
- **Hardware Acceleration**: Uses GPU-accelerated compositing for optimal performance

#### The Magic Behind the Scenes

```swift
// 🔒 Complete screen capture exclusion
if #available(macOS 11.0, *) {
    window.sharingType = .none  // Invisible to all capture APIs
}

// 👻 Input transparency - clicks pass through
window.ignoresMouseEvents = true

// 🏔️ Strategic window positioning
window.level = .screenSaver  // Above content, below system UI

// 🌐 Universal compatibility
window.collectionBehavior = [
    .canJoinAllSpaces,      // Works on all Spaces
    .fullScreenAuxiliary    // Appears over full-screen apps
]
```

#### What This Means for You

1. **🔍 Local Visibility**: You see the overlay clearly on your screen
2. **📹 Screen Share Invisibility**: Completely hidden from Zoom, Teams, Meet, etc.
3. **🖱️ Full Interaction**: Click, type, and interact normally in the masked area
4. **🎯 Precision Coverage**: Exactly covers the right quarter of your main display
5. **⚡ Zero Performance Impact**: No effect on screen sharing application performance

## 🚀 Installation

### Quick Install (Recommended)

```bash
# Clone or download the project
cd Screen_hider

# Build the application
./build.sh

# Install to Applications folder
./install.sh
```

### Manual Installation

#### Step 1: Build the Application

```bash
# Navigate to project directory
cd Screen_hider

# Build with default settings
./build.sh

# Or build with custom options
BUILD_TYPE=release UNIVERSAL=true ./build.sh
```

#### Step 2: Install

```bash
# Install to Applications folder with auto-launch setup
./install.sh

# Or manually copy to Applications
cp -R ScreenHider.app /Applications/
```

### Build Options

| Option | Description | Example |
|--------|-------------|---------|
| `BUILD_TYPE=release` | Optimized release build | `BUILD_TYPE=release ./build.sh` |
| `UNIVERSAL=true` | Universal binary (Intel + Apple Silicon) | `UNIVERSAL=true ./build.sh` |
| `SIGN_APP=true` | Code sign the application | `SIGN_APP=true ./build.sh` |

### Advanced Build Configuration

```bash
# Development build (default)
./build.sh

# Production release build
BUILD_TYPE=release UNIVERSAL=true SIGN_APP=true ./build.sh

# Clean build environment
./clean.sh && ./build.sh
```

## 📱 Usage

### Getting Started

1. **Launch ScreenHider**
   ```bash
   open /Applications/ScreenHider.app
   # Or double-click the app in Applications folder
   ```

2. **Find the Menu Bar Icon**
   - Look for a small rectangle icon in your menu bar (top-right area)
   - The icon changes color when the overlay is active

3. **Toggle the Overlay**
   - **Left-click** the menu bar icon for quick toggle
   - **Right-click** for full menu options
   - **Keyboard shortcut**: `Cmd+Shift+H`

### Menu Options

| Option | Description |
|--------|-------------|
| **Show/Hide Overlay** | Toggle the screen mask on/off |
| **Overlay Color** | Choose from multiple color options |
| **Opacity** | Adjust transparency level |
| **Preferences** | Configure application settings |
| **Help** | Quick help and shortcuts |
| **About** | Application information |

### Keyboard Shortcuts

- `Cmd+Shift+H` - Toggle overlay on/off
- `Cmd+,` - Open preferences (from menu)
- `Cmd+Q` - Quit application (from menu)

### Testing Screen Sharing

1. **Enable the Overlay**
   - Use any method above to show the overlay
   - Verify you can see the black/colored rectangle on the right quarter of your screen

2. **Start Screen Sharing**
   - Open Zoom, Microsoft Teams, Google Meet, or any screen sharing app
   - Start sharing your entire screen
   - **Important**: Share the entire screen, not just a window

3. **Verify Exclusion**
   - The overlay should be **visible to you** locally
   - The overlay should be **invisible to other participants**
   - You can still interact with applications in the masked area

4. **Test Interaction**
   - Click and type in the masked area
   - Applications should respond normally
   - The overlay doesn't block any functionality

### Validation Tools

```bash
# Test the application
./test.sh

# Validate screen capture exclusion
./validate_screen_capture.sh

# Clean build artifacts
./clean.sh
```

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### 🔍 Overlay Appears in Screen Sharing

**Symptoms**: Other participants can see the overlay during screen sharing

**Solutions**:
- ✅ Ensure you're running **macOS 11.0 or later**
- ✅ Restart ScreenHider: `pkill ScreenHider && open /Applications/ScreenHider.app`
- ✅ Rebuild the application: `./clean.sh && ./build.sh`
- ✅ Check compilation target: Ensure built for macOS 11.0+

#### 👻 Overlay Doesn't Appear

**Symptoms**: No visible overlay on screen when enabled

**Solutions**:
- ✅ Check menu bar icon status (should show green indicator when active)
- ✅ Try toggling: `Cmd+Shift+H` or click menu bar icon
- ✅ Verify screen detection: Check if main screen is properly detected
- ✅ Restart application with logging: Run from Terminal to see debug output

#### 🖱️ Input Events Blocked

**Symptoms**: Cannot click or type in the masked area

**Solutions**:
- ✅ This should never happen - if it does, immediately restart the app
- ✅ Check window configuration: Overlay should have `ignoresMouseEvents = true`
- ✅ Force quit and restart: `pkill -f ScreenHider && open /Applications/ScreenHider.app`

#### 🖥️ Multiple Monitor Issues

**Symptoms**: Overlay appears on wrong screen or doesn't adjust to screen changes

**Solutions**:
- ✅ Overlay targets the **main screen** (with menu bar) by default
- ✅ Screen changes are automatically detected and handled
- ✅ If issues persist, toggle overlay off/on after screen configuration changes
- ✅ Check System Preferences > Displays > Arrangement for main screen setting

#### 🚀 Application Won't Start

**Symptoms**: App doesn't launch or crashes immediately

**Solutions**:
- ✅ Check macOS version: `sw_vers -productVersion`
- ✅ Verify app permissions: Right-click app > Open (for unsigned apps)
- ✅ Check Console.app for crash logs
- ✅ Rebuild application: `./clean.sh && ./build.sh`

### Advanced Troubleshooting

#### Debug Mode

```bash
# Run with debug output
./ScreenHider.app/Contents/MacOS/ScreenHider

# Check system logs
log stream --predicate 'process == "ScreenHider"' --level debug
```

#### Reset Preferences

```bash
# Clear all saved preferences
defaults delete com.screenhider.app

# Remove launch agent (if installed)
launchctl unload ~/Library/LaunchAgents/com.screenhider.app.plist
rm ~/Library/LaunchAgents/com.screenhider.app.plist
```

#### Validation Tests

```bash
# Run comprehensive tests
./test.sh

# Test screen capture exclusion specifically
./validate_screen_capture.sh
```

## 🏗️ Development

### Project Structure

```
Screen_hider/
├── 📄 main.swift              # Application entry point
├── 🎛️ AppDelegate.swift       # Main controller & menu bar interface
├── 🪟 OverlayWindow.swift     # Specialized overlay window
├── 📺 ScreenManager.swift     # Screen detection utilities
├── ⚙️ Info.plist             # App configuration
├── 🔨 build.sh               # Enhanced build script
├── 🧪 test.sh                # Comprehensive test suite
├── 🔍 validate_screen_capture.sh # Screen capture validation
├── 📦 install.sh             # Installation script
├── 🧹 clean.sh               # Cleanup script
├── 📚 README.md              # This documentation
└── 📋 TECHNICAL_DETAILS.md   # Technical implementation guide
```

### Key Components

| Component | Purpose | Key Features |
|-----------|---------|--------------|
| **`AppDelegate`** | Application lifecycle & UI | Menu bar interface, preferences, hotkeys |
| **`OverlayWindow`** | Screen overlay management | Screen capture exclusion, input transparency |
| **`ScreenManager`** | Display handling | Multi-monitor support, dynamic positioning |

### Build System Features

- **🏗️ Multi-architecture support** (Intel + Apple Silicon)
- **🔒 Code signing integration**
- **⚡ Optimized release builds**
- **🧪 Comprehensive testing**
- **📦 Automated installation**

### Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Test thoroughly**: `./test.sh && ./validate_screen_capture.sh`
4. **Commit changes**: `git commit -m 'Add amazing feature'`
5. **Push to branch**: `git push origin feature/amazing-feature`
6. **Open a Pull Request**

### Development Workflow

```bash
# Setup development environment
git clone <repository>
cd Screen_hider

# Development cycle
./clean.sh                    # Clean previous builds
./build.sh                    # Build application
./test.sh                     # Run tests
./validate_screen_capture.sh  # Test core functionality

# Install for testing
./install.sh
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Support

### Getting Help

- 📖 **Documentation**: Check this README and `TECHNICAL_DETAILS.md`
- 🧪 **Testing**: Run `./test.sh` for diagnostics
- 🔍 **Validation**: Use `./validate_screen_capture.sh` to test core functionality
- 🐛 **Issues**: Check the troubleshooting section above

### Compatibility

| Platform | Status | Notes |
|----------|--------|-------|
| **macOS 11.0+** | ✅ Fully Supported | Required for screen capture exclusion |
| **Intel Macs** | ✅ Fully Supported | Native x86_64 support |
| **Apple Silicon** | ✅ Fully Supported | Native arm64 support |
| **Universal Binary** | ✅ Available | Use `UNIVERSAL=true ./build.sh` |

### Performance

- **Memory Usage**: < 10MB RAM
- **CPU Usage**: < 0.1% when idle
- **Startup Time**: < 1 second
- **Screen Sharing Impact**: Zero performance overhead

---

<div align="center">

**Made with ❤️ for privacy-conscious professionals**

*ScreenHider helps you maintain privacy during screen sharing while keeping full productivity*

</div>
