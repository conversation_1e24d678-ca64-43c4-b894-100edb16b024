#!/bin/bash

# ScreenHider Clean Script
# This script cleans all build artifacts and temporary files

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }

echo "🧹 Cleaning ScreenHider build artifacts..."

# Remove build artifacts
log_info "Removing executables..."
rm -f ScreenHider ScreenHider_arm64 ScreenHider_x86_64

log_info "Removing app bundle..."
rm -rf ScreenHider.app

log_info "Removing temporary files..."
rm -f *.dSYM
rm -rf build/
rm -f .DS_Store

log_info "Removing user preferences (optional)..."
defaults delete com.screenhider.app 2>/dev/null || true

log_success "Clean completed!"
echo ""
echo "To rebuild the application, run: ./build.sh"
