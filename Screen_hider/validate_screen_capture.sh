#!/bin/bash

# ScreenHider Screen Capture Validation Script
# This script tests the screen capture exclusion functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

echo "🔍 ScreenHider Screen Capture Validation"
echo "========================================"

# Check if app is running
log_info "Checking if ScreenHider is running..."
if pgrep -f "ScreenHider" > /dev/null; then
    log_success "ScreenHider is running"
else
    log_warning "ScreenHider is not running. Starting it now..."
    open ScreenHider.app
    sleep 3
    
    if pgrep -f "ScreenHider" > /dev/null; then
        log_success "ScreenHider started successfully"
    else
        log_error "Failed to start ScreenHider"
        exit 1
    fi
fi

# Function to take a screenshot and analyze it
test_screenshot() {
    local test_name="$1"
    local screenshot_path="/tmp/screenhider_test_${test_name}.png"
    
    log_info "Taking screenshot for test: $test_name"
    
    # Take screenshot
    screencapture -x "$screenshot_path"
    
    if [ -f "$screenshot_path" ]; then
        local file_size=$(stat -f%z "$screenshot_path")
        log_success "Screenshot saved: $screenshot_path (${file_size} bytes)"
        
        # Get image dimensions
        if command -v sips &> /dev/null; then
            local dimensions=$(sips -g pixelWidth -g pixelHeight "$screenshot_path" | grep -E "pixelWidth|pixelHeight" | awk '{print $2}' | tr '\n' 'x' | sed 's/x$//')
            log_info "Screenshot dimensions: $dimensions"
        fi
        
        return 0
    else
        log_error "Failed to take screenshot"
        return 1
    fi
}

# Test 1: Screenshot without overlay
log_info "Test 1: Taking screenshot without overlay..."
echo "Please ensure the overlay is HIDDEN, then press Enter to continue..."
read -r

test_screenshot "without_overlay"

# Test 2: Screenshot with overlay
log_info "Test 2: Taking screenshot with overlay..."
echo ""
echo "Now please:"
echo "1. Show the overlay (click menu bar icon or use Cmd+Shift+H)"
echo "2. Verify you can see the black overlay on your screen"
echo "3. Press Enter to take a screenshot"
read -r

test_screenshot "with_overlay"

# Test 3: Window capture test
log_info "Test 3: Testing window capture exclusion..."
if command -v screencapture &> /dev/null; then
    log_info "Taking window-specific screenshot..."
    screencapture -l$(osascript -e 'tell app "Finder" to id of window 1') /tmp/screenhider_window_test.png 2>/dev/null || log_warning "Window capture test skipped"
fi

# Test 4: QuickTime test (if available)
log_info "Test 4: QuickTime screen recording test..."
if [ -d "/Applications/QuickTime Player.app" ]; then
    log_info "QuickTime Player found. You can manually test screen recording:"
    echo "1. Open QuickTime Player"
    echo "2. File > New Screen Recording"
    echo "3. Start recording with overlay visible"
    echo "4. Check if overlay appears in the recording"
else
    log_warning "QuickTime Player not found, skipping recording test"
fi

# Test 5: Check window properties
log_info "Test 5: Checking overlay window properties..."
python3 -c "
import subprocess
import json

try:
    # Get window information using system_profiler or other methods
    result = subprocess.run(['osascript', '-e', '''
        tell application \"System Events\"
            set windowList to {}
            repeat with proc in processes
                if name of proc is \"ScreenHider\" then
                    repeat with win in windows of proc
                        set end of windowList to {name of win, position of win, size of win}
                    end repeat
                end if
            end repeat
            return windowList
        end tell
    '''], capture_output=True, text=True)
    
    if result.returncode == 0:
        print('✅ Window information retrieved')
        print(f'   Output: {result.stdout.strip()}')
    else:
        print('⚠️  Could not retrieve window information')
        
except Exception as e:
    print(f'❌ Error checking window properties: {e}')
" 2>/dev/null || log_warning "Window property check failed"

# Test 6: Verify sharingType setting
log_info "Test 6: Verifying screen sharing exclusion..."
echo ""
echo "Manual verification steps:"
echo "1. Open Zoom, Teams, or Google Meet"
echo "2. Start screen sharing"
echo "3. Ensure overlay is visible on your screen"
echo "4. Check if overlay appears in the shared video"
echo "5. The overlay should NOT be visible to other participants"

# Summary
echo ""
log_success "Screen capture validation completed!"
echo ""
echo "📊 Test Results Summary:"
echo "   • Screenshots taken and saved to /tmp/"
echo "   • Check the screenshots to verify overlay exclusion"
echo "   • Manual screen sharing test recommended"
echo ""
echo "🔍 Expected Results:"
echo "   • Screenshots should NOT show the overlay"
echo "   • Screen sharing should NOT show the overlay to others"
echo "   • You should still see the overlay locally"
echo ""
echo "📁 Screenshot files:"
ls -la /tmp/screenhider_test_*.png 2>/dev/null || echo "   No screenshots found"
echo ""
echo "🧹 To clean up test files:"
echo "   rm /tmp/screenhider_test_*.png"
