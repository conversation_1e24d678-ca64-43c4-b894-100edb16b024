#!/bin/bash

# Test script to verify ScreenHider overlay visibility and functionality
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

echo "🧪 ScreenHider Overlay Visibility Test"
echo "======================================"

# Check if ScreenHider is running
if ! pgrep -f "ScreenHider" > /dev/null; then
    log_error "ScreenHider is not running. Please start it first."
    exit 1
fi

log_success "ScreenHider is running"

echo ""
echo "📋 MANUAL TEST STEPS:"
echo "===================="

echo ""
echo "1️⃣  CHECK MENU BAR ICON:"
echo "   • Look for a rectangle icon in your menu bar"
echo "   • If you don't see it, the app may not have started properly"

echo ""
echo "2️⃣  ENABLE OVERLAY:"
echo "   • Right-click the ScreenHider menu bar icon"
echo "   • Click 'Show Overlay'"
echo "   • You should see a colored overlay appear on the right quarter of your screen"

echo ""
echo "3️⃣  TEST VISIBILITY:"
echo "   • The overlay should be clearly visible on your screen"
echo "   • It should stay on top of other windows"
echo "   • Try different opacity levels from the menu"

echo ""
echo "4️⃣  TEST SCREEN CAPTURE EXCLUSION:"
echo "   • Take a screenshot (Cmd+Shift+3)"
echo "   • The overlay should NOT appear in the screenshot"
echo "   • Test with Zoom/Teams - participants should NOT see the overlay"

echo ""
echo "🎯 EXPECTED BEHAVIOR:"
echo "==================="
echo "✅ Menu bar icon visible"
echo "✅ Overlay appears when enabled"
echo "✅ Overlay stays on top of other windows"
echo "✅ Overlay responds to opacity changes"
echo "❌ Overlay NOT visible in screenshots"
echo "❌ Overlay NOT visible in screen sharing"

echo ""
echo "🔧 TROUBLESHOOTING:"
echo "=================="
echo "• If overlay doesn't appear: Try restarting ScreenHider"
echo "• If overlay is too transparent: Increase opacity from menu"
echo "• If overlay appears in screen sharing: Check Console.app for errors"

echo ""
log_info "Test instructions complete. Please follow the manual steps above."

# Optional: Try to get window information
echo ""
echo "🔍 TECHNICAL INFO:"
echo "=================="
log_info "Checking for ScreenHider windows..."

# Use system_profiler to check for windows (if available)
if command -v osascript &> /dev/null; then
    WINDOW_COUNT=$(osascript -e 'tell application "System Events" to count (every window of every process whose name is "ScreenHider")' 2>/dev/null || echo "unknown")
    log_info "ScreenHider windows detected: $WINDOW_COUNT"
fi

echo ""
log_info "If the overlay is not visible, please restart ScreenHider and try again."
