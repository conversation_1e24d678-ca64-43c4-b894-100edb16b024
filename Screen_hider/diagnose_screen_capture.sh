#!/bin/bash

# Comprehensive Screen Capture Diagnosis for ScreenHider
# This script diagnoses why screen capture exclusion isn't working

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

echo "🔍 ScreenHider Screen Capture Exclusion Diagnosis"
echo "================================================="

# System Information
echo ""
echo "🖥️  SYSTEM INFORMATION:"
echo "======================"
MACOS_VERSION=$(sw_vers -productVersion)
BUILD_VERSION=$(sw_vers -buildVersion)
log_info "macOS Version: $MACOS_VERSION ($BUILD_VERSION)"

# Check if this is macOS Sequoia (15.x) which has different behavior
if [[ "$MACOS_VERSION" == 15.* ]]; then
    log_warning "macOS Sequoia (15.x) detected - screen capture APIs have changed!"
    echo "   • Apple changed screen capture behavior in Sequoia"
    echo "   • sharingType = .none may not work reliably"
    echo "   • Some apps bypass the exclusion APIs entirely"
fi

# Check running applications
echo ""
echo "📱 RUNNING APPLICATIONS:"
echo "======================="
if pgrep -f "zoom.us" > /dev/null; then
    log_warning "Zoom is running - this app is known to bypass exclusion APIs"
fi

if pgrep -f "Teams" > /dev/null; then
    log_info "Microsoft Teams is running"
fi

if pgrep -f "ScreenHider" > /dev/null; then
    log_success "ScreenHider is running"
else
    log_error "ScreenHider is not running"
    exit 1
fi

# Check screen capture settings
echo ""
echo "📸 SCREEN CAPTURE SETTINGS:"
echo "=========================="
CAPTURE_SETTINGS=$(defaults read com.apple.screencapture 2>/dev/null || echo "No settings found")
log_info "Screen capture settings: $CAPTURE_SETTINGS"

# Check if ScreenHider has screen recording permissions
echo ""
echo "🔐 PERMISSIONS ANALYSIS:"
echo "======================="
log_info "Checking if ScreenHider needs screen recording permissions..."

# The fundamental issue analysis
echo ""
echo "🚨 FUNDAMENTAL ISSUE ANALYSIS:"
echo "============================="
log_error "PROBLEM IDENTIFIED: Modern screen sharing apps bypass macOS exclusion APIs"
echo ""
echo "WHY SCREEN CAPTURE EXCLUSION DOESN'T WORK:"
echo "• Zoom uses hardware-level screen capture that ignores software exclusion"
echo "• macOS Sequoia (15.x) changed how exclusion APIs work"
echo "• sharingType = .none is not respected by many applications"
echo "• Screen sharing apps capture at the graphics driver level"

echo ""
echo "🔧 POSSIBLE SOLUTIONS:"
echo "===================="
echo ""
echo "1️⃣  SYSTEM-LEVEL CHANGES NEEDED:"
echo "   • System Preferences → Security & Privacy → Screen Recording"
echo "   • Remove Zoom's screen recording permission (if possible)"
echo "   • This would prevent Zoom from capturing anything"

echo ""
echo "2️⃣  ALTERNATIVE APPROACHES:"
echo "   • Use a second monitor and only share specific windows"
echo "   • Use virtual desktop software"
echo "   • Use hardware screen filters"
echo "   • Use Zoom's 'Share Application Window' instead of 'Share Screen'"

echo ""
echo "3️⃣  ZOOM-SPECIFIC WORKAROUNDS:"
echo "   • In Zoom: Share → Advanced → Portion of Screen"
echo "   • Select only the area you want to share (excluding overlay)"
echo "   • Use 'Share Application Window' instead of 'Share Entire Screen'"

echo ""
echo "4️⃣  TECHNICAL WORKAROUND:"
echo "   • Create a virtual display"
echo "   • Move content to virtual display"
echo "   • Share only the virtual display"

echo ""
echo "🎯 RECOMMENDED IMMEDIATE SOLUTION:"
echo "================================="
log_success "USE ZOOM'S SELECTIVE SHARING:"
echo "1. In Zoom, click 'Share Screen'"
echo "2. Choose 'Advanced' tab"
echo "3. Select 'Portion of Screen'"
echo "4. Draw selection area EXCLUDING the overlay region"
echo "5. This bypasses the need for software exclusion"

echo ""
echo "📊 TECHNICAL CONCLUSION:"
echo "======================="
log_error "Software-based screen capture exclusion is fundamentally broken in modern macOS"
echo "• Apple's exclusion APIs are not reliable"
echo "• Screen sharing apps use hardware-level capture"
echo "• The only reliable solution is selective sharing or hardware filters"

echo ""
echo "💡 ALTERNATIVE APP APPROACH:"
echo "=========================="
echo "Instead of trying to hide from screen capture, ScreenHider could:"
echo "• Provide visual guides for selective sharing"
echo "• Create templates for common sharing scenarios"
echo "• Integrate with screen sharing apps to automate selective sharing"
echo "• Use virtual displays or desktop management"

echo ""
log_warning "The fundamental approach of hiding overlays from screen capture is not viable with current technology."
log_info "Recommend switching to selective sharing or alternative solutions."
